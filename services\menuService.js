const db = require('../config/database');

class MenuService {
  // Lấy tất cả menu items theo hierarchy
  async getAllMenus() {
    try {
      // Lấy tất cả menu items
      const allMenus = await db.query(`
        SELECT m.*, t.name as table_name, t.display_name as table_display_name
        FROM admin_menus m
        LEFT JOIN admintable t ON m.table_id = t.id
        WHERE m.is_active = true
        ORDER BY m.order_index ASC
      `);

      // Tạo hierarchy
      const menuMap = new Map();
      const rootMenus = [];

      // Tạo map cho tất cả menus
      allMenus.forEach(menu => {
        menu.children = [];
        menuMap.set(menu.id, menu);
      });

      // Xây dựng hierarchy
      allMenus.forEach(menu => {
        if (menu.parent_id === null) {
          rootMenus.push(menu);
        } else {
          const parent = menuMap.get(menu.parent_id);
          if (parent) {
            parent.children.push(menu);
          }
        }
      });

      return rootMenus;
    } catch (error) {
      console.error('Error getting all menus:', error);
      throw error;
    }
  }

  // Lấy menu theo ID
  async getMenuById(id) {
    try {
      const menu = await db.queryOne(`
        SELECT m.*, t.name as table_name, t.display_name as table_display_name
        FROM admin_menus m
        LEFT JOIN admintable t ON m.table_id = t.id
        WHERE m.id = ?
      `, [id]);

      if (!menu) return null;

      // Lấy children
      menu.children = await db.query(`
        SELECT * FROM admin_menus 
        WHERE parent_id = ? AND is_active = true
        ORDER BY order_index ASC
      `, [id]);

      // Lấy parent
      if (menu.parent_id) {
        menu.parent = await db.queryOne(`
          SELECT * FROM admin_menus WHERE id = ?
        `, [menu.parent_id]);
      }

      return menu;
    } catch (error) {
      console.error('Error getting menu by ID:', error);
      throw error;
    }
  }

  // Tạo menu mới
  async createMenu(menuData) {
    try {
      const result = await db.query(`
        INSERT INTO admin_menus (
          title, url, icon, parent_id, order_index, is_active, 
          is_divider, is_title, target, badge_text, badge_color, table_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        menuData.title,
        menuData.url || null,
        menuData.icon || null,
        menuData.parent_id ? parseInt(menuData.parent_id) : null,
        parseInt(menuData.order_index) || 0,
        menuData.is_active !== undefined ? menuData.is_active : true,
        menuData.is_divider || false,
        menuData.is_title || false,
        menuData.target || null,
        menuData.badge_text || null,
        menuData.badge_color || null,
        menuData.table_id ? parseInt(menuData.table_id) : null
      ]);

      return await this.getMenuById(result.insertId);
    } catch (error) {
      console.error('Error creating menu:', error);
      throw error;
    }
  }

  // Cập nhật menu
  async updateMenu(id, menuData) {
    try {
      await db.query(`
        UPDATE admin_menus 
        SET title = ?, url = ?, icon = ?, parent_id = ?, order_index = ?,
            is_active = ?, is_divider = ?, is_title = ?, target = ?,
            badge_text = ?, badge_color = ?, table_id = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        menuData.title,
        menuData.url || null,
        menuData.icon || null,
        menuData.parent_id ? parseInt(menuData.parent_id) : null,
        parseInt(menuData.order_index) || 0,
        menuData.is_active !== undefined ? menuData.is_active : true,
        menuData.is_divider || false,
        menuData.is_title || false,
        menuData.target || null,
        menuData.badge_text || null,
        menuData.badge_color || null,
        menuData.table_id ? parseInt(menuData.table_id) : null,
        id
      ]);

      return await this.getMenuById(id);
    } catch (error) {
      console.error('Error updating menu:', error);
      throw error;
    }
  }

  // Xóa menu
  async deleteMenu(id) {
    try {
      const result = await db.query('DELETE FROM admin_menus WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting menu:', error);
      throw error;
    }
  }

  // Tự động tạo menu cho các bảng admin
  async syncTableMenus() {
    try {
      // Lấy tất cả admin tables
      const tables = await db.query(`
        SELECT * FROM admintable 
        WHERE is_active = true 
        ORDER BY order_index ASC
      `);

      // Tìm hoặc tạo parent menu "Data Management"
      let dataManagementMenu = await db.queryOne(`
        SELECT * FROM admin_menus 
        WHERE title = 'Data Management' AND parent_id IS NULL
      `);

      if (!dataManagementMenu) {
        const result = await db.query(`
          INSERT INTO admin_menus (title, url, icon, parent_id, order_index, is_active, is_title)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, ['Data Management', null, 'cil-storage', null, 100, true, true]);
        
        dataManagementMenu = { id: result.insertId };
      }

      // Tạo menu cho từng table
      for (const table of tables) {
        // Kiểm tra xem menu đã tồn tại chưa
        const existingMenu = await db.queryOne(`
          SELECT * FROM admin_menus WHERE table_id = ?
        `, [table.id]);

        if (!existingMenu) {
          await db.query(`
            INSERT INTO admin_menus (title, url, icon, parent_id, order_index, is_active, table_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `, [
            table.display_name,
            `/admin/tables/${table.id}/data`,
            table.icon || 'cil-grid',
            dataManagementMenu.id,
            table.order_index,
            true,
            table.id
          ]);
        } else {
          // Cập nhật menu hiện có
          await db.query(`
            UPDATE admin_menus 
            SET title = ?, url = ?, icon = ?, order_index = ?, is_active = ?
            WHERE id = ?
          `, [
            table.display_name,
            `/admin/tables/${table.id}/data`,
            table.icon || 'cil-grid',
            table.order_index,
            table.is_active,
            existingMenu.id
          ]);
        }
      }

      return { success: true, message: 'Table menus synced successfully' };
    } catch (error) {
      console.error('Error syncing table menus:', error);
      throw error;
    }
  }

  // Lấy menu dạng flat list (cho admin management)
  async getFlatMenuList() {
    try {
      return await db.query(`
        SELECT m.*, 
               p.title as parent_title,
               t.name as table_name, t.display_name as table_display_name
        FROM admin_menus m
        LEFT JOIN admin_menus p ON m.parent_id = p.id
        LEFT JOIN admintable t ON m.table_id = t.id
        ORDER BY m.order_index ASC
      `);
    } catch (error) {
      console.error('Error getting flat menu list:', error);
      throw error;
    }
  }

  // Cập nhật thứ tự menu
  async updateMenuOrder(menuOrders) {
    const connection = await db.beginTransaction();
    try {
      for (const order of menuOrders) {
        await db.query(`
          UPDATE admin_menus 
          SET order_index = ?, parent_id = ?
          WHERE id = ?
        `, [order.order_index, order.parent_id || null, order.id]);
      }
      
      await db.commitTransaction(connection);
      return { success: true, message: 'Menu order updated successfully' };
    } catch (error) {
      await db.rollbackTransaction(connection);
      console.error('Error updating menu order:', error);
      throw error;
    }
  }

  // Tạo menu mặc định
  async createDefaultMenus() {
    try {
      const defaultMenus = [
        {
          title: 'Dashboard',
          url: '/admin',
          icon: 'speedometer',
          parent_id: null,
          order_index: 0,
          is_active: true
        },
        {
          title: 'Tables',
          url: '/admin/tables',
          icon: 'list',
          parent_id: null,
          order_index: 1,
          is_active: true
        },
        {
          title: 'Users',
          url: '/admin/users',
          icon: 'people',
          parent_id: null,
          order_index: 2,
          is_active: true
        },
        {
          title: 'Menu',
          url: '/admin/menus',
          icon: 'menu',
          parent_id: null,
          order_index: 3,
          is_active: true
        }
      ];

      for (const menu of defaultMenus) {
        const existing = await db.queryOne(`
          SELECT * FROM admin_menus WHERE title = ? AND parent_id IS NULL
        `, [menu.title]);
        
        if (!existing) {
          await db.query(`
            INSERT INTO admin_menus (title, url, icon, parent_id, order_index, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [
            menu.title,
            menu.url,
            menu.icon,
            menu.parent_id,
            menu.order_index,
            menu.is_active
          ]);
        }
      }

      return { success: true, message: 'Default menus created successfully' };
    } catch (error) {
      console.error('Error creating default menus:', error);
      throw error;
    }
  }
}

module.exports = new MenuService();
