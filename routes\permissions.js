const express = require('express');
const router = express.Router();
const permissionController = require('../controller/permissionController');
const rolePermissionController = require('../controller/rolePermissionController');
const { checkSpecificPermission, requireAdmin, injectUserPermissions } = require('../middleware/permissionMiddleware');

// Middleware inject user permissions cho tất cả routes
router.use(injectUserPermissions());

// ========== PERMISSION ROUTES ==========

// Danh sách permissions
router.get('/', 
  checkSpecificPermission('read_permissions'),
  permissionController.index
);

// API DataTables cho permissions
router.get('/api', 
  checkSpecificPermission('read_permissions'),
  permissionController.api
);

// API lấy filter options
router.get('/filter-options', 
  checkSpecificPermission('read_permissions'),
  permissionController.filterOptions
);

// Form tạo permission mới
router.get('/create', 
  checkSpecificPermission('add_permissions'),
  permissionController.create
);

// Lưu permission mới
router.post('/', 
  checkSpecificPermission('add_permissions'),
  permissionController.store
);

// Chi tiết permission
router.get('/:id', 
  checkSpecificPermission('read_permissions'),
  permissionController.show
);

// Form chỉnh sửa permission
router.get('/:id/edit', 
  checkSpecificPermission('edit_permissions'),
  permissionController.edit
);

// Cập nhật permission
router.put('/:id', 
  checkSpecificPermission('edit_permissions'),
  permissionController.update
);

// Xóa permission
router.delete('/:id', 
  checkSpecificPermission('delete_permissions'),
  permissionController.destroy
);

// Đồng bộ permissions cho tất cả bảng
router.post('/sync', 
  requireAdmin(),
  permissionController.sync
);

// ========== TABLE PERMISSIONS API ==========

// Lấy permissions của bảng cụ thể
router.get('/table/:tableName', 
  checkSpecificPermission('read_permissions'),
  permissionController.getTablePermissions
);

// Tạo permissions cho bảng mới
router.post('/table/create', 
  requireAdmin(),
  permissionController.createTablePermissions
);

// ========== ROLE PERMISSIONS ROUTES ==========

// Quản lý quyền của role
router.get('/roles/:roleId', 
  checkSpecificPermission('read_roles'),
  rolePermissionController.index
);

// Gán quyền cho role
router.post('/roles/:roleId/grant', 
  checkSpecificPermission('edit_roles'),
  rolePermissionController.grant
);

// Thu hồi quyền từ role
router.post('/roles/:roleId/revoke', 
  checkSpecificPermission('edit_roles'),
  rolePermissionController.revoke
);

// Gán hàng loạt quyền cho role
router.post('/roles/:roleId/grant-bulk', 
  checkSpecificPermission('edit_roles'),
  rolePermissionController.grantBulk
);

// Thu hồi hàng loạt quyền từ role
router.post('/roles/:roleId/revoke-bulk', 
  checkSpecificPermission('edit_roles'),
  rolePermissionController.revokeBulk
);

// Gán tất cả quyền của bảng cho role
router.post('/roles/:roleId/grant-table', 
  checkSpecificPermission('edit_roles'),
  rolePermissionController.grantTablePermissions
);

// Thu hồi tất cả quyền của bảng từ role
router.post('/roles/:roleId/revoke-table', 
  checkSpecificPermission('edit_roles'),
  rolePermissionController.revokeTablePermissions
);

// ========== API ROUTES ==========

// Lấy danh sách roles (API)
router.get('/api/roles', 
  checkSpecificPermission('read_roles'),
  rolePermissionController.getRolesList
);

// Lấy permissions của role (API)
router.get('/api/roles/:roleId/permissions', 
  checkSpecificPermission('read_roles'),
  rolePermissionController.getRolePermissions
);

module.exports = router; 