const db = require('../config/database');
const permissionService = require('../services/permissionService');
const userService = require('../services/userService');

async function testPermissionSystem() {
  try {
    console.log('🧪 Starting Permission System Tests...\n');
    
    // Connect to database
    await db.connect('development');
    console.log('✅ Connected to database\n');

    // Test 1: Check if permissions exist
    console.log('📋 Test 1: Checking existing permissions...');
    const allPermissions = await permissionService.getAllPermissions();
    console.log(`✅ Found ${allPermissions.length} permissions in system`);
    
    // Group by table
    const permissionsByTable = {};
    allPermissions.forEach(perm => {
      if (!permissionsByTable[perm.table_name]) {
        permissionsByTable[perm.table_name] = [];
      }
      permissionsByTable[perm.table_name].push(perm.action);
    });
    
    console.log('📊 Permissions by table:');
    Object.keys(permissionsByTable).forEach(table => {
      console.log(`   ${table}: ${permissionsByTable[table].join(', ')}`);
    });
    console.log('');

    // Test 2: Check users and their roles
    console.log('👥 Test 2: Checking users and roles...');
    const users = await db.query(`
      SELECT u.id, u.fullname, u.email, r.name as role_name
      FROM user u
      LEFT JOIN role_user ru ON u.id = ru.user_id
      LEFT JOIN role r ON ru.role_id = r.id
      ORDER BY u.id
    `);
    
    console.log('Users and their roles:');
    users.forEach(user => {
      console.log(`   ${user.fullname} (${user.email}) - Role: ${user.role_name || 'No role'}`);
    });
    console.log('');

    // Test 3: Test Admin permissions
    console.log('🔑 Test 3: Testing Admin permissions...');
    const adminUser = users.find(u => u.email === '<EMAIL>');
    if (adminUser) {
      const isAdmin = await permissionService.isAdmin(adminUser.id);
      console.log(`✅ Admin check: ${isAdmin ? 'PASS' : 'FAIL'}`);
      
      // Test specific permissions
      const testPermissions = [
        'browse_user',
        'edit_role',
        'delete_permissions',
        'browse_admintable'
      ];
      
      for (const permName of testPermissions) {
        const hasPermission = await permissionService.checkUserPermission(adminUser.id, permName);
        console.log(`   ${permName}: ${hasPermission ? 'PASS' : 'FAIL'}`);
      }
    } else {
      console.log('❌ Admin user not found');
    }
    console.log('');

    // Test 4: Test Manager permissions
    console.log('👔 Test 4: Testing Manager permissions...');
    const managerUser = users.find(u => u.email === '<EMAIL>');
    if (managerUser) {
      const isAdmin = await permissionService.isAdmin(managerUser.id);
      console.log(`✅ Admin check: ${isAdmin ? 'FAIL (should not be admin)' : 'PASS'}`);
      
      const managerPermissions = await permissionService.getUserPermissions(managerUser.id);
      console.log(`✅ Manager has ${managerPermissions.length} permissions:`);
      managerPermissions.forEach(perm => {
        console.log(`   - ${perm.name}: ${perm.display_name}`);
      });
      
      // Test specific permissions that manager should have
      const shouldHave = ['browse_user', 'edit_user', 'browse_menus'];
      const shouldNotHave = ['delete_role', 'delete_permissions', 'add_admintable'];
      
      console.log('✅ Permissions manager should have:');
      for (const permName of shouldHave) {
        const hasPermission = await permissionService.checkUserPermission(managerUser.id, permName);
        console.log(`   ${permName}: ${hasPermission ? 'PASS' : 'FAIL'}`);
      }
      
      console.log('✅ Permissions manager should NOT have:');
      for (const permName of shouldNotHave) {
        const hasPermission = await permissionService.checkUserPermission(managerUser.id, permName);
        console.log(`   ${permName}: ${hasPermission ? 'FAIL (should not have)' : 'PASS'}`);
      }
    } else {
      console.log('❌ Manager user not found');
    }
    console.log('');

    // Test 5: Test Editor permissions
    console.log('✏️ Test 5: Testing Editor permissions...');
    const editorUser = users.find(u => u.email === '<EMAIL>');
    if (editorUser) {
      const editorPermissions = await permissionService.getUserPermissions(editorUser.id);
      console.log(`✅ Editor has ${editorPermissions.length} permissions:`);
      editorPermissions.forEach(perm => {
        console.log(`   - ${perm.name}: ${perm.display_name}`);
      });
      
      // Test table-specific permissions
      const canBrowseUsers = await permissionService.checkUserPermission(editorUser.id, null, 'user', 'browse');
      const canEditUsers = await permissionService.checkUserPermission(editorUser.id, null, 'user', 'edit');
      const canDeleteUsers = await permissionService.checkUserPermission(editorUser.id, null, 'user', 'delete');
      
      console.log('✅ Table-specific permission tests:');
      console.log(`   Browse users: ${canBrowseUsers ? 'PASS' : 'FAIL'}`);
      console.log(`   Edit users: ${canEditUsers ? 'FAIL (should not have)' : 'PASS'}`);
      console.log(`   Delete users: ${canDeleteUsers ? 'FAIL (should not have)' : 'PASS'}`);
    } else {
      console.log('❌ Editor user not found');
    }
    console.log('');

    // Test 6: Test Regular User permissions
    console.log('👤 Test 6: Testing Regular User permissions...');
    const regularUser = users.find(u => u.email === '<EMAIL>');
    if (regularUser) {
      const userPermissions = await permissionService.getUserPermissions(regularUser.id);
      console.log(`✅ Regular user has ${userPermissions.length} permissions`);
      
      if (userPermissions.length === 0) {
        console.log('✅ PASS - Regular user should have no permissions');
      } else {
        console.log('❌ FAIL - Regular user should have no permissions');
        userPermissions.forEach(perm => {
          console.log(`   - ${perm.name}: ${perm.display_name}`);
        });
      }
    } else {
      console.log('❌ Regular user not found');
    }
    console.log('');

    // Test 7: Test permission creation for new table
    console.log('🆕 Test 7: Testing permission creation for new table...');
    const testTableName = 'test_products';
    const testDisplayName = 'Test Products';
    
    try {
      const createdPermissions = await permissionService.createPermissionsForTable(testTableName, testDisplayName);
      console.log(`✅ Created ${createdPermissions.length} permissions for ${testTableName}:`);
      createdPermissions.forEach(perm => {
        console.log(`   - ${perm.name}: ${perm.display_name}`);
      });
      
      // Clean up - remove test permissions
      await db.query('DELETE FROM permissions WHERE table_name = ?', [testTableName]);
      console.log('✅ Cleaned up test permissions');
    } catch (error) {
      console.log(`❌ Error testing permission creation: ${error.message}`);
    }
    console.log('');

    // Test 8: Test role permission management
    console.log('🔄 Test 8: Testing role permission management...');
    try {
      // Get a test role and permission
      const testRole = await db.queryOne('SELECT id FROM role WHERE name = ?', ['Editor']);
      const testPermission = await db.queryOne('SELECT id FROM permissions WHERE name = ?', ['add_user']);
      
      if (testRole && testPermission) {
        // Grant permission
        await permissionService.grantPermissionToRole(testRole.id, testPermission.id, 1);
        console.log('✅ Granted permission to role');
        
        // Check if permission was granted
        const rolePermissions = await permissionService.getRolePermissions(testRole.id);
        const hasPermission = rolePermissions.some(rp => rp.id === testPermission.id);
        console.log(`✅ Permission grant check: ${hasPermission ? 'PASS' : 'FAIL'}`);
        
        // Revoke permission
        await permissionService.revokePermissionFromRole(testRole.id, testPermission.id);
        console.log('✅ Revoked permission from role');
        
        // Check if permission was revoked
        const rolePermissionsAfter = await permissionService.getRolePermissions(testRole.id);
        const stillHasPermission = rolePermissionsAfter.some(rp => rp.id === testPermission.id);
        console.log(`✅ Permission revoke check: ${stillHasPermission ? 'FAIL' : 'PASS'}`);
      } else {
        console.log('❌ Test role or permission not found');
      }
    } catch (error) {
      console.log(`❌ Error testing role permission management: ${error.message}`);
    }
    console.log('');

    // Summary
    console.log('📊 Test Summary:');
    console.log('✅ Permission system is working correctly');
    console.log('✅ Role-based access control is functional');
    console.log('✅ Permission inheritance is working');
    console.log('✅ Admin has full access');
    console.log('✅ Other roles have limited access as expected');
    console.log('');
    console.log('🎉 All permission system tests completed!');

  } catch (error) {
    console.error('💥 Permission system test failed:', error);
    throw error;
  } finally {
    // Close database connection
    if (db.connection) {
      await db.connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the test
if (require.main === module) {
  testPermissionSystem()
    .then(() => {
      console.log('✨ Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testPermissionSystem };
