-- =====================================================
-- Complete Database Schema for Backend CoreUI Project
-- Dynamic CRUD Admin System with Permission Management
-- =====================================================

SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS `role_permissions`;
DROP TABLE IF EXISTS `permissions`;
DROP TABLE IF EXISTS `user_sessions`;
DROP TABLE IF EXISTS `user_session_settings`;
DROP TABLE IF EXISTS `user_role`;
DROP TABLE IF EXISTS `role_user`;
DROP TABLE IF EXISTS `adminrelation`;
DROP TABLE IF EXISTS `admincolumn`;
DROP TABLE IF EXISTS `admintable`;
DROP TABLE IF EXISTS `admin_menus`;
DROP TABLE IF EXISTS `role`;
DROP TABLE IF EXISTS `user`;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 1. USER MANAGEMENT TABLES
-- =====================================================

-- Users table
CREATE TABLE `user` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `fullname` VARCHAR(255) NOT NULL,
  `email` VARCHAR(255) NOT NULL UNIQUE,
  `password` VARCHAR(255) NOT NULL,
  `phone` VARCHAR(20) NULL,
  `gender` ENUM('male', 'female', 'other') NULL,
  `active` BOOLEAN DEFAULT TRUE,
  `session_id` VARCHAR(255) NULL,
  `jwt_token_id` VARCHAR(255) NULL, -- For backward compatibility with single device mode
  `device_info` TEXT NULL, -- For backward compatibility
  `token_created_at` TIMESTAMP NULL, -- For backward compatibility
  `last_login` TIMESTAMP NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX `idx_email` (`email`),
  INDEX `idx_active` (`active`),
  INDEX `idx_jwt_token_id` (`jwt_token_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions table for JWT token management and multi-device support
CREATE TABLE `user_sessions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `jwt_token_id` VARCHAR(255) NOT NULL UNIQUE,
  `token_id` VARCHAR(255) NULL, -- For backward compatibility
  `device_name` VARCHAR(255) NULL,
  `device_type` VARCHAR(50) NULL, -- mobile, tablet, desktop
  `browser` VARCHAR(100) NULL,
  `os` VARCHAR(100) NULL,
  `device_info` TEXT NULL,
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `login_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `last_activity` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expires_at` TIMESTAMP NULL,
  `is_active` TINYINT(1) DEFAULT 1,
  `is_current_session` TINYINT(1) DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `last_used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_jwt_token_id` (`jwt_token_id`),
  INDEX `idx_token_id` (`token_id`),
  INDEX `idx_expires_at` (`expires_at`),
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User session settings table for multi-device configuration
CREATE TABLE `user_session_settings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL UNIQUE,
  `max_sessions` INT DEFAULT 5,
  `session_timeout_hours` INT DEFAULT 24,
  `allow_multiple_devices` TINYINT(1) DEFAULT 1,
  `notify_new_login` TINYINT(1) DEFAULT 1,
  `auto_logout_inactive` TINYINT(1) DEFAULT 1,
  `inactive_timeout_hours` INT DEFAULT 72,
  `require_2fa_new_device` TINYINT(1) DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE,
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Roles table
CREATE TABLE `role` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL UNIQUE,
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User-Role relationship table
CREATE TABLE `user_role` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `role_id` INT NOT NULL,
  `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `assigned_by` INT NULL,
  `is_active` BOOLEAN DEFAULT TRUE,

  UNIQUE KEY `unique_user_role` (`user_id`, `role_id`),
  FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`role_id`) REFERENCES `role`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assigned_by`) REFERENCES `user`(`id`) ON DELETE SET NULL,

  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. PERMISSION MANAGEMENT TABLES
-- =====================================================

-- Permissions table
CREATE TABLE `permissions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL UNIQUE,
  `display_name` VARCHAR(255) NOT NULL,
  `description` TEXT NULL,
  `table_name` VARCHAR(255) NULL,
  `action` VARCHAR(50) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX `idx_table_action` (`table_name`, `action`),
  INDEX `idx_name` (`name`),
  INDEX `idx_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Role-Permission relationship table
CREATE TABLE `role_permissions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `role_id` INT NOT NULL,
  `permission_id` INT NOT NULL,
  `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `granted_by` INT NULL,
  
  UNIQUE KEY `unique_role_permission` (`role_id`, `permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `role`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`granted_by`) REFERENCES `user`(`id`) ON DELETE SET NULL,
  
  INDEX `idx_role_id` (`role_id`),
  INDEX `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. DYNAMIC ADMIN SYSTEM TABLES
-- =====================================================

-- Admin tables metadata
CREATE TABLE `admintable` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL UNIQUE,
  `display_name` VARCHAR(255) NOT NULL,
  `description` TEXT NULL,
  `icon` VARCHAR(100) NULL,
  `model_name` VARCHAR(255) NOT NULL,
  `is_active` BOOLEAN DEFAULT TRUE,
  `order_index` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX `idx_name` (`name`),
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_order` (`order_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin columns metadata
CREATE TABLE `admincolumn` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `table_id` INT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `display_name` VARCHAR(255) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `length` INT NULL,
  `is_nullable` BOOLEAN DEFAULT TRUE,
  `is_primary` BOOLEAN DEFAULT FALSE,
  `is_unique` BOOLEAN DEFAULT FALSE,
  `default_value` TEXT NULL,
  `is_auto_increment` BOOLEAN DEFAULT FALSE,
  `is_visible_list` BOOLEAN DEFAULT TRUE,
  `is_visible_form` BOOLEAN DEFAULT TRUE,
  `is_searchable` BOOLEAN DEFAULT FALSE,
  `is_sortable` BOOLEAN DEFAULT TRUE,
  `form_type` VARCHAR(50) DEFAULT 'text',
  `validation_rules` TEXT NULL,
  `order_index` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (`table_id`) REFERENCES `admintable`(`id`) ON DELETE CASCADE,
  INDEX `idx_table_id` (`table_id`),
  INDEX `idx_name` (`name`),
  INDEX `idx_order` (`order_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin relations metadata (foreign keys)
CREATE TABLE `adminrelation` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `table_id` INT NOT NULL,
  `column_id` INT NOT NULL,
  `foreign_table_id` INT NOT NULL,
  `foreign_column` VARCHAR(255) NOT NULL DEFAULT 'id',
  `display_column` VARCHAR(255) NULL,
  `relation_type` VARCHAR(50) DEFAULT 'belongs_to',
  `on_delete` VARCHAR(50) DEFAULT 'RESTRICT',
  `on_update` VARCHAR(50) DEFAULT 'CASCADE',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (`table_id`) REFERENCES `admintable`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`column_id`) REFERENCES `admincolumn`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`foreign_table_id`) REFERENCES `admintable`(`id`) ON DELETE CASCADE,
  
  INDEX `idx_table_id` (`table_id`),
  INDEX `idx_column_id` (`column_id`),
  INDEX `idx_foreign_table_id` (`foreign_table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. MENU MANAGEMENT TABLE
-- =====================================================

-- Admin menus table
CREATE TABLE `admin_menus` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `title` VARCHAR(255) NOT NULL,
  `url` VARCHAR(500) NULL,
  `icon` VARCHAR(100) NULL,
  `parent_id` INT NULL,
  `order_index` INT NOT NULL DEFAULT 0,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `is_divider` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_title` BOOLEAN NOT NULL DEFAULT FALSE,
  `target` VARCHAR(50) NULL,
  `badge_text` VARCHAR(50) NULL,
  `badge_color` VARCHAR(50) NULL,
  `table_id` INT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (`parent_id`) REFERENCES `admin_menus`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`table_id`) REFERENCES `admintable`(`id`) ON DELETE CASCADE,
  
  INDEX `idx_parent_id` (`parent_id`),
  INDEX `idx_table_id` (`table_id`),
  INDEX `idx_order` (`order_index`),
  INDEX `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. SAMPLE DATA INSERTION
-- =====================================================

-- Insert sample roles
INSERT INTO `role` (`id`, `name`, `description`) VALUES
(1, 'Admin', 'Administrator role with full access to all features'),
(2, 'Manager', 'Manager role with limited admin access'),
(3, 'Editor', 'Editor role with content management access'),
(4, 'User', 'Regular user role with basic access');

-- Insert sample users (password is 'password123' hashed with bcrypt)
INSERT INTO `user` (`id`, `fullname`, `email`, `password`, `active`) VALUES
(1, 'System Administrator', '<EMAIL>', '$2b$10$p9NrgWDZ4JkpRLwsD61KSOquX5jDjUFBCL3rc79TmIdHT6ER7j1fq', 1),
(2, 'John Manager', '<EMAIL>', '$2b$10$p9NrgWDZ4JkpRLwsD61KSOquX5jDjUFBCL3rc79TmIdHT6ER7j1fq', 1),
(3, 'Jane Editor', '<EMAIL>', '$2b$10$p9NrgWDZ4JkpRLwsD61KSOquX5jDjUFBCL3rc79TmIdHT6ER7j1fq', 1),
(4, 'Bob User', '<EMAIL>', '$2b$10$p9NrgWDZ4JkpRLwsD61KSOquX5jDjUFBCL3rc79TmIdHT6ER7j1fq', 1);

-- Assign roles to users
INSERT INTO `user_role` (`user_id`, `role_id`, `assigned_by`) VALUES
(1, 1, 1), -- Admin -> Admin role
(2, 2, 1), -- Manager -> Manager role
(3, 3, 1), -- Editor -> Editor role
(4, 4, 1); -- User -> User role

-- Insert default session settings for all users
INSERT INTO `user_session_settings` (`user_id`, `max_sessions`, `session_timeout_hours`, `allow_multiple_devices`, `notify_new_login`, `auto_logout_inactive`, `inactive_timeout_hours`, `require_2fa_new_device`) VALUES
(1, 10, 24, 1, 1, 1, 72, 0), -- Admin: 10 sessions, 24h timeout, multi-device enabled
(2, 5, 12, 1, 1, 1, 48, 0),  -- Manager: 5 sessions, 12h timeout, multi-device enabled
(3, 3, 8, 1, 1, 1, 24, 0),   -- Editor: 3 sessions, 8h timeout, multi-device enabled
(4, 2, 4, 0, 1, 1, 12, 0);   -- User: 2 sessions, 4h timeout, single device only

-- Insert system permissions
INSERT INTO `permissions` (`name`, `display_name`, `description`, `table_name`, `action`) VALUES
-- Table management permissions (admintable)
('browse_admintable', 'Browse Admin Tables', 'Quyền truy cập danh sách bảng và quản lý cấu trúc bảng', 'admintable', 'browse'),
('read_admintable', 'Read Admin Tables', 'Quyền xem chi tiết cấu trúc bảng', 'admintable', 'read'),
('edit_admintable', 'Edit Admin Tables', 'Quyền chỉnh sửa cấu trúc bảng', 'admintable', 'edit'),
('add_admintable', 'Add Admin Tables', 'Quyền tạo bảng mới', 'admintable', 'add'),
('delete_admintable', 'Delete Admin Tables', 'Quyền xóa bảng', 'admintable', 'delete'),

-- Menu management permissions (admin_menus)
('browse_admin_menus', 'Browse Admin Menus', 'Quyền truy cập danh sách menu', 'admin_menus', 'browse'),
('read_admin_menus', 'Read Admin Menus', 'Quyền xem chi tiết menu', 'admin_menus', 'read'),
('edit_admin_menus', 'Edit Admin Menus', 'Quyền chỉnh sửa menu', 'admin_menus', 'edit'),
('add_admin_menus', 'Add Admin Menus', 'Quyền tạo menu mới', 'admin_menus', 'add'),
('delete_admin_menus', 'Delete Admin Menus', 'Quyền xóa menu', 'admin_menus', 'delete'),

-- Role management permissions (role)
('browse_role', 'Browse Roles', 'Quyền truy cập danh sách vai trò', 'role', 'browse'),
('read_role', 'Read Roles', 'Quyền xem chi tiết vai trò', 'role', 'read'),
('edit_role', 'Edit Roles', 'Quyền chỉnh sửa vai trò', 'role', 'edit'),
('add_role', 'Add Roles', 'Quyền tạo vai trò mới', 'role', 'add'),
('delete_role', 'Delete Roles', 'Quyền xóa vai trò', 'role', 'delete'),

-- Permission management permissions (permissions)
('browse_permissions', 'Browse Permissions', 'Quyền truy cập danh sách quyền', 'permissions', 'browse'),
('read_permissions', 'Read Permissions', 'Quyền xem chi tiết quyền', 'permissions', 'read'),
('edit_permissions', 'Edit Permissions', 'Quyền chỉnh sửa quyền', 'permissions', 'edit'),
('add_permissions', 'Add Permissions', 'Quyền tạo quyền mới', 'permissions', 'add'),
('delete_permissions', 'Delete Permissions', 'Quyền xóa quyền', 'permissions', 'delete'),

-- User management permissions
('browse_user', 'Browse Users', 'Quyền truy cập danh sách người dùng', 'user', 'browse'),
('read_user', 'Read Users', 'Quyền xem chi tiết người dùng', 'user', 'read'),
('edit_user', 'Edit Users', 'Quyền chỉnh sửa người dùng', 'user', 'edit'),
('add_user', 'Add Users', 'Quyền tạo người dùng mới', 'user', 'add'),
('delete_user', 'Delete Users', 'Quyền xóa người dùng', 'user', 'delete'),

-- User-Role relationship permissions
('browse_user_role', 'Browse User Roles', 'Quyền truy cập danh sách phân quyền người dùng', 'user_role', 'browse'),
('read_user_role', 'Read User Roles', 'Quyền xem chi tiết phân quyền người dùng', 'user_role', 'read'),
('edit_user_role', 'Edit User Roles', 'Quyền chỉnh sửa phân quyền người dùng', 'user_role', 'edit'),
('add_user_role', 'Add User Roles', 'Quyền tạo phân quyền người dùng mới', 'user_role', 'add'),
('delete_user_role', 'Delete User Roles', 'Quyền xóa phân quyền người dùng', 'user_role', 'delete');

-- Grant all permissions to Admin role
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 1, p.id, 1 FROM `permissions` p;

-- Grant limited permissions to Manager role
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 2, p.id, 1 FROM `permissions` p
WHERE p.name IN (
  'browse_user', 'read_user', 'edit_user', 'add_user',
  'browse_user_role', 'read_user_role', 'edit_user_role', 'add_user_role',
  'browse_admin_menus', 'read_admin_menus', 'edit_admin_menus'
);

-- Grant basic permissions to Editor role
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 3, p.id, 1 FROM `permissions` p
WHERE p.name IN (
  'browse_user', 'read_user',
  'browse_admin_menus', 'read_admin_menus'
);

-- =====================================================
-- 6. ADMIN METADATA FOR SYSTEM TABLES
-- =====================================================

-- Insert admin table metadata for system tables
INSERT INTO `admintable` (`name`, `display_name`, `description`, `icon`, `model_name`, `is_active`, `order_index`) VALUES
('user', 'Users', 'Quản lý người dùng hệ thống', 'fas fa-users', 'User', 1, 1),
('role', 'Roles', 'Quản lý vai trò người dùng', 'fas fa-user-tag', 'Role', 1, 2),
('user_role', 'User Roles', 'Quản lý phân quyền người dùng', 'fas fa-link', 'UserRole', 1, 3),
('permissions', 'Permissions', 'Quản lý quyền hệ thống', 'fas fa-key', 'Permission', 1, 4),
('role_permissions', 'Role Permissions', 'Quản lý quyền của vai trò', 'fas fa-shield-alt', 'RolePermission', 1, 5),
('user_sessions', 'User Sessions', 'Quản lý phiên đăng nhập', 'fas fa-desktop', 'UserSession', 1, 6),
('user_session_settings', 'Session Settings', 'Cài đặt phiên đăng nhập', 'fas fa-cog', 'UserSessionSettings', 1, 7),
('admintable', 'Admin Tables', 'Quản lý cấu trúc bảng', 'fas fa-table', 'AdminTable', 1, 8),
('admin_menus', 'Admin Menus', 'Quản lý menu hệ thống', 'fas fa-bars', 'AdminMenu', 1, 9);

-- Insert admin column metadata for user table
INSERT INTO `admincolumn` (`table_id`, `name`, `display_name`, `type`, `length`, `is_nullable`, `is_primary`, `is_visible_list`, `is_visible_form`, `is_searchable`, `order_index`) VALUES
((SELECT id FROM admintable WHERE name = 'user'), 'id', 'ID', 'int', NULL, 0, 1, 1, 0, 0, 0),
((SELECT id FROM admintable WHERE name = 'user'), 'fullname', 'Full Name', 'varchar', 255, 0, 0, 1, 1, 1, 1),
((SELECT id FROM admintable WHERE name = 'user'), 'email', 'Email', 'varchar', 255, 0, 0, 1, 1, 1, 2),
((SELECT id FROM admintable WHERE name = 'user'), 'password', 'Password', 'varchar', 255, 0, 0, 0, 1, 0, 3),
((SELECT id FROM admintable WHERE name = 'user'), 'phone', 'Phone', 'varchar', 20, 1, 0, 1, 1, 1, 4),
((SELECT id FROM admintable WHERE name = 'user'), 'gender', 'Gender', 'enum', NULL, 1, 0, 1, 1, 0, 5),
((SELECT id FROM admintable WHERE name = 'user'), 'active', 'Active', 'boolean', NULL, 0, 0, 1, 1, 0, 6),
((SELECT id FROM admintable WHERE name = 'user'), 'jwt_token_id', 'JWT Token ID', 'varchar', 255, 1, 0, 0, 0, 0, 7),
((SELECT id FROM admintable WHERE name = 'user'), 'device_info', 'Device Info', 'text', NULL, 1, 0, 0, 0, 0, 8),
((SELECT id FROM admintable WHERE name = 'user'), 'token_created_at', 'Token Created At', 'timestamp', NULL, 1, 0, 1, 0, 0, 9),
((SELECT id FROM admintable WHERE name = 'user'), 'last_login', 'Last Login', 'timestamp', NULL, 1, 0, 1, 0, 0, 10),
((SELECT id FROM admintable WHERE name = 'user'), 'created_at', 'Created At', 'timestamp', NULL, 0, 0, 1, 0, 0, 11),
((SELECT id FROM admintable WHERE name = 'user'), 'updated_at', 'Updated At', 'timestamp', NULL, 0, 0, 1, 0, 0, 12);

-- Insert admin column metadata for role table
INSERT INTO `admincolumn` (`table_id`, `name`, `display_name`, `type`, `length`, `is_nullable`, `is_primary`, `is_visible_list`, `is_visible_form`, `is_searchable`, `order_index`) VALUES
((SELECT id FROM admintable WHERE name = 'role'), 'id', 'ID', 'int', NULL, 0, 1, 1, 0, 0, 0),
((SELECT id FROM admintable WHERE name = 'role'), 'name', 'Name', 'varchar', 255, 0, 0, 1, 1, 1, 1),
((SELECT id FROM admintable WHERE name = 'role'), 'description', 'Description', 'text', NULL, 1, 0, 1, 1, 1, 2),
((SELECT id FROM admintable WHERE name = 'role'), 'created_at', 'Created At', 'timestamp', NULL, 0, 0, 1, 0, 0, 3),
((SELECT id FROM admintable WHERE name = 'role'), 'updated_at', 'Updated At', 'timestamp', NULL, 0, 0, 1, 0, 0, 4);

-- Insert admin column metadata for role_user table
INSERT INTO `admincolumn` (`table_id`, `name`, `display_name`, `type`, `length`, `is_nullable`, `is_primary`, `is_visible_list`, `is_visible_form`, `is_searchable`, `order_index`) VALUES
((SELECT id FROM admintable WHERE name = 'role_user'), 'id', 'ID', 'int', NULL, 0, 1, 1, 0, 0, 0),
((SELECT id FROM admintable WHERE name = 'role_user'), 'user_id', 'User', 'int', NULL, 0, 0, 1, 1, 1, 1),
((SELECT id FROM admintable WHERE name = 'role_user'), 'role_id', 'Role', 'int', NULL, 0, 0, 1, 1, 1, 2),
((SELECT id FROM admintable WHERE name = 'role_user'), 'assigned_at', 'Assigned At', 'timestamp', NULL, 0, 0, 1, 0, 0, 3),
((SELECT id FROM admintable WHERE name = 'role_user'), 'is_active', 'Active', 'boolean', NULL, 0, 0, 1, 1, 0, 4);

-- Insert admin column metadata for permissions table
INSERT INTO `admincolumn` (`table_id`, `name`, `display_name`, `type`, `length`, `is_nullable`, `is_primary`, `is_visible_list`, `is_visible_form`, `is_searchable`, `order_index`) VALUES
((SELECT id FROM admintable WHERE name = 'permissions'), 'id', 'ID', 'int', NULL, 0, 1, 1, 0, 0, 0),
((SELECT id FROM admintable WHERE name = 'permissions'), 'name', 'Name', 'varchar', 255, 0, 0, 1, 1, 1, 1),
((SELECT id FROM admintable WHERE name = 'permissions'), 'display_name', 'Display Name', 'varchar', 255, 0, 0, 1, 1, 1, 2),
((SELECT id FROM admintable WHERE name = 'permissions'), 'description', 'Description', 'text', NULL, 1, 0, 1, 1, 1, 3),
((SELECT id FROM admintable WHERE name = 'permissions'), 'table_name', 'Table Name', 'varchar', 255, 1, 0, 1, 1, 1, 4),
((SELECT id FROM admintable WHERE name = 'permissions'), 'action', 'Action', 'varchar', 50, 0, 0, 1, 1, 1, 5),
((SELECT id FROM admintable WHERE name = 'permissions'), 'created_at', 'Created At', 'timestamp', NULL, 0, 0, 1, 0, 0, 6);

-- Insert admin column metadata for user_sessions table
INSERT INTO `admincolumn` (`table_id`, `name`, `display_name`, `type`, `length`, `is_nullable`, `is_primary`, `is_visible_list`, `is_visible_form`, `is_searchable`, `order_index`) VALUES
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'id', 'ID', 'int', NULL, 0, 1, 1, 0, 0, 0),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'user_id', 'User', 'int', NULL, 0, 0, 1, 1, 1, 1),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'jwt_token_id', 'Token ID', 'varchar', 255, 0, 0, 0, 0, 0, 2),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'device_name', 'Device Name', 'varchar', 255, 1, 0, 1, 1, 1, 3),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'device_type', 'Device Type', 'varchar', 50, 1, 0, 1, 1, 1, 4),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'browser', 'Browser', 'varchar', 100, 1, 0, 1, 1, 1, 5),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'os', 'Operating System', 'varchar', 100, 1, 0, 1, 1, 1, 6),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'ip_address', 'IP Address', 'varchar', 45, 1, 0, 1, 1, 1, 7),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'login_at', 'Login At', 'timestamp', NULL, 0, 0, 1, 0, 0, 8),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'last_activity', 'Last Activity', 'timestamp', NULL, 0, 0, 1, 0, 0, 9),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'is_active', 'Active', 'boolean', NULL, 0, 0, 1, 1, 0, 10),
((SELECT id FROM admintable WHERE name = 'user_sessions'), 'is_current_session', 'Current Session', 'boolean', NULL, 0, 0, 1, 0, 0, 11);

-- Insert admin column metadata for user_session_settings table
INSERT INTO `admincolumn` (`table_id`, `name`, `display_name`, `type`, `length`, `is_nullable`, `is_primary`, `is_visible_list`, `is_visible_form`, `is_searchable`, `order_index`) VALUES
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'id', 'ID', 'int', NULL, 0, 1, 1, 0, 0, 0),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'user_id', 'User', 'int', NULL, 0, 0, 1, 1, 1, 1),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'max_sessions', 'Max Sessions', 'int', NULL, 0, 0, 1, 1, 0, 2),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'session_timeout_hours', 'Session Timeout (Hours)', 'int', NULL, 0, 0, 1, 1, 0, 3),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'allow_multiple_devices', 'Allow Multiple Devices', 'boolean', NULL, 0, 0, 1, 1, 0, 4),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'notify_new_login', 'Notify New Login', 'boolean', NULL, 0, 0, 1, 1, 0, 5),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'auto_logout_inactive', 'Auto Logout Inactive', 'boolean', NULL, 0, 0, 1, 1, 0, 6),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'inactive_timeout_hours', 'Inactive Timeout (Hours)', 'int', NULL, 0, 0, 1, 1, 0, 7),
((SELECT id FROM admintable WHERE name = 'user_session_settings'), 'require_2fa_new_device', 'Require 2FA New Device', 'boolean', NULL, 0, 0, 1, 1, 0, 8);

-- Insert admin relations for foreign keys
INSERT INTO `adminrelation` (`table_id`, `column_id`, `foreign_table_id`, `foreign_column`, `display_column`, `relation_type`) VALUES
-- role_user relations
((SELECT id FROM admintable WHERE name = 'role_user'),
 (SELECT id FROM admincolumn WHERE table_id = (SELECT id FROM admintable WHERE name = 'role_user') AND name = 'user_id'),
 (SELECT id FROM admintable WHERE name = 'user'), 'id', 'fullname', 'belongs_to'),
((SELECT id FROM admintable WHERE name = 'role_user'),
 (SELECT id FROM admincolumn WHERE table_id = (SELECT id FROM admintable WHERE name = 'role_user') AND name = 'role_id'),
 (SELECT id FROM admintable WHERE name = 'role'), 'id', 'name', 'belongs_to'),
-- user_sessions relations
((SELECT id FROM admintable WHERE name = 'user_sessions'),
 (SELECT id FROM admincolumn WHERE table_id = (SELECT id FROM admintable WHERE name = 'user_sessions') AND name = 'user_id'),
 (SELECT id FROM admintable WHERE name = 'user'), 'id', 'fullname', 'belongs_to'),
-- user_session_settings relations
((SELECT id FROM admintable WHERE name = 'user_session_settings'),
 (SELECT id FROM admincolumn WHERE table_id = (SELECT id FROM admintable WHERE name = 'user_session_settings') AND name = 'user_id'),
 (SELECT id FROM admintable WHERE name = 'user'), 'id', 'fullname', 'belongs_to');

-- =====================================================
-- 7. SAMPLE MENU DATA
-- =====================================================

-- Insert sample menu structure
INSERT INTO `admin_menus` (`title`, `url`, `icon`, `parent_id`, `order_index`, `is_active`, `is_title`, `table_id`) VALUES
-- Main navigation
('Dashboard', '/admin', 'fas fa-tachometer-alt', NULL, 1, 1, 0, NULL),
('User Management', NULL, 'fas fa-users-cog', NULL, 2, 1, 1, NULL),
('System Management', NULL, 'fas fa-cogs', NULL, 3, 1, 1, NULL),

-- User Management submenu
('Users', '/admin/crud/user', 'fas fa-users', (SELECT id FROM admin_menus WHERE title = 'User Management' LIMIT 1), 1, 1, 0, (SELECT id FROM admintable WHERE name = 'user')),
('Roles', '/admin/crud/role', 'fas fa-user-tag', (SELECT id FROM admin_menus WHERE title = 'User Management' LIMIT 1), 2, 1, 0, (SELECT id FROM admintable WHERE name = 'role')),
('User Roles', '/admin/crud/user_role', 'fas fa-link', (SELECT id FROM admin_menus WHERE title = 'User Management' LIMIT 1), 3, 1, 0, (SELECT id FROM admintable WHERE name = 'user_role')),
('Permissions', '/admin/crud/permissions', 'fas fa-key', (SELECT id FROM admin_menus WHERE title = 'User Management' LIMIT 1), 4, 1, 0, (SELECT id FROM admintable WHERE name = 'permissions')),

-- System Management submenu
('Tables', '/admin/crud/admintable', 'fas fa-table', (SELECT id FROM admin_menus WHERE title = 'System Management' LIMIT 1), 1, 1, 0, (SELECT id FROM admintable WHERE name = 'admintable')),
('Menus', '/admin/crud/admin_menus', 'fas fa-bars', (SELECT id FROM admin_menus WHERE title = 'System Management' LIMIT 1), 2, 1, 0, (SELECT id FROM admintable WHERE name = 'admin_menus'));

-- =====================================================
-- 8. INDEXES AND OPTIMIZATIONS
-- =====================================================

-- Additional indexes for performance
CREATE INDEX `idx_user_email_active` ON `user` (`email`, `active`);
CREATE INDEX `idx_permissions_table_action` ON `permissions` (`table_name`, `action`);
CREATE INDEX `idx_role_permissions_lookup` ON `role_permissions` (`role_id`, `permission_id`);
CREATE INDEX `idx_user_role_lookup` ON `user_role` (`user_id`, `role_id`, `is_active`);
CREATE INDEX `idx_admincolumn_table_visible` ON `admincolumn` (`table_id`, `is_visible_list`, `order_index`);
CREATE INDEX `idx_admin_menus_hierarchy` ON `admin_menus` (`parent_id`, `order_index`, `is_active`);

-- =====================================================
-- 9. COMPLETION MESSAGE
-- =====================================================

-- Database schema creation completed
SELECT 'Database schema created successfully!' as message,
       (SELECT COUNT(*) FROM user) as users_count,
       (SELECT COUNT(*) FROM role) as roles_count,
       (SELECT COUNT(*) FROM permissions) as permissions_count,
       (SELECT COUNT(*) FROM admintable) as admin_tables_count,
       (SELECT COUNT(*) FROM admin_menus) as menus_count;
