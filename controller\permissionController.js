const permissionService = require('../services/permissionService');
const commonService = require('../services/commonService');
const db = require('../config/database');

class PermissionController {

  // =====================================================
  // PERMISSION MANAGEMENT PAGES
  // =====================================================

  // Trang danh sách permissions
  async index(req, res) {
    try {
      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: true},
      ];
      
      res.render('admin/permissions', {
        title: 'Quản lý Permissions',
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions index:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  // Trang tạo permission mới
  async create(req, res) {
    try {
      // Get available tables
      const tables = await commonService.getAllDataTable(
        'admintable',
        'name, display_name',
        'is_active = 1',
        [],
        'display_name'
      );

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: false},
        {name: 'Create', url:'/admin/permissions/create', active: true},
      ];

      res.render('admin/permission-form', {
        title: 'Tạo Permission Mới',
        permission: null,
        tables,
        actions: ['browse', 'read', 'edit', 'add', 'delete'],
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions create:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  // Xử lý tạo permission
  async store(req, res) {
    try {
      const { name, display_name, description, table_name, action } = req.body;

      // Validate input
      if (!name || !display_name || !action) {
        return res.status(400).json({
          success: false,
          message: 'Tên, tên hiển thị và hành động là bắt buộc'
        });
      }

      // Check if permission already exists
      const existing = await commonService.getRecordTable('permissions', 'name = ?', [name]);
      if (existing) {
        return res.status(400).json({
          success: false,
          message: 'Tên permission đã tồn tại'
        });
      }

      // Create permission
      const permissionId = await permissionService.createPermission({
        name,
        display_name,
        description,
        table_name,
        action
      });

      res.json({
        success: true,
        message: 'Permission đã được tạo thành công',
        data: { id: permissionId }
      });
    } catch (error) {
      console.error('Error creating permission:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi tạo permission: ' + error.message
      });
    }
  }

  // Hiển thị chi tiết permission
  async show(req, res) {
    try {
      const { id } = req.params;

      const permission = await commonService.getAllDataTable('permissions', {id: id});
      if (!permission) {
        return res.status(404).render('404', { message: 'Permission không tồn tại' });
      }

      // Lấy danh sách roles có permission này
      const roles = await db.query(`
        SELECT r.id, r.name, rp.granted_at
        FROM role_permissions rp
        INNER JOIN role r ON rp.role_id = r.id
        WHERE rp.permission_id = ?
        ORDER BY r.name
      `, [id]);

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: false},
        {name: 'Detail', url:'/admin/permissions', active: true},
      ];

      res.render('admin/permission-detail', {
        title: `Permission: ${permission.display_name}`,
        permission,
        roles,
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions show:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  // Trang chỉnh sửa permission
  async edit(req, res) {
    try {
      const { id } = req.params;
      
      const permission = await commonService.getRecordTable('permissions', 'id = ?', [id]);
      if (!permission) {
        return res.status(404).render('404', { message: 'Permission không tồn tại' });
      }

      // Get available tables
      const tables = await commonService.getAllDataTable(
        'admintable',
        'name, display_name',
        'is_active = 1',
        [],
        'display_name'
      );

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Permissions', url:'/admin/permissions', active: false},
        {name: 'Edit', url:'/admin/permissions/edit', active: true},
      ];

      res.render('admin/permission-form', {
        title: 'Chỉnh sửa Permission',
        permission,
        tables,
        actions: ['browse', 'read', 'edit', 'add', 'delete'],
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in permissions edit:', error);
      res.status(500).render('500', { error: error.message });
    }
  }

  // Xử lý cập nhật permission
  async update(req, res) {
    try {
      const { id } = req.params;
      const { display_name, description } = req.body;

      // Validate input
      if (!display_name) {
        return res.status(400).json({
          success: false,
          message: 'Tên hiển thị là bắt buộc'
        });
      }

      // Check if permission exists
      const permission = await commonService.getRecordTable('permissions', 'id = ?', [id]);
      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission không tồn tại'
        });
      }

      // Update permission
      await permissionService.updatePermission(id, {
        display_name,
        description
      });

      res.json({
        success: true,
        message: 'Permission đã được cập nhật thành công'
      });
    } catch (error) {
      console.error('Error updating permission:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi cập nhật permission: ' + error.message
      });
    }
  }

  // Xóa permission
  async destroy(req, res) {
    try {
      const { id } = req.params;

      // Check if permission exists
      const permission = await commonService.getRecordTable('permissions', 'id = ?', [id]);
      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission không tồn tại'
        });
      }

      // Check if any roles are using this permission
      const roleCount = await db.queryOne(`
        SELECT COUNT(*) as count FROM role_permissions WHERE permission_id = ?
      `, [id]);

      if (roleCount.count > 0) {
        return res.status(400).json({
          success: false,
          message: `Không thể xóa permission này vì có ${roleCount.count} role đang sử dụng`
        });
      }

      // Delete permission
      await permissionService.deletePermission(id);

      res.json({
        success: true,
        message: 'Permission đã được xóa thành công'
      });
    } catch (error) {
      console.error('Error deleting permission:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi xóa permission: ' + error.message
      });
    }
  }

  // =====================================================
  // ROLE PERMISSION MANAGEMENT
  // =====================================================

  // Trang quản lý quyền của role
  async rolePermissions(req, res) {
    try {
      const roleId = req.params.roleId;

      // Get role info
      const role = await commonService.getRecordTable('role', 'id = ?', [roleId]);
      if (!role) {
        return res.status(404).render('admin/error', {
          title: 'Not Found',
          message: 'Role not found'
        });
      }

      // Get all permissions grouped by table
      const allPermissions = await permissionService.getAllPermissions();
      const rolePermissions = await permissionService.getRolePermissions(roleId);

      // Group permissions by table
      const permissionsByTable = {};
      allPermissions.forEach(permission => {
        if (!permissionsByTable[permission.table_name]) {
          permissionsByTable[permission.table_name] = [];
        }
        permission.hasPermission = rolePermissions.some(rp => rp.id === permission.id);
        permissionsByTable[permission.table_name].push(permission);
      });

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Roles', url:'/admin/roles', active: false},
        {name: 'Permissions', url:'#', active: true},
      ];

      res.render('admin/permissions/role-permissions', {
        title: `Manage Permissions - ${role.name}`,
        role: role,
        permissionsByTable: permissionsByTable,
        rolePermissions: rolePermissions,
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in role permissions:', error);
      res.status(500).render('admin/error', {
        title: 'Error',
        message: 'Error loading role permissions page'
      });
    }
  }

  // Cập nhật quyền của role
  async updateRolePermissions(req, res) {
    try {
      const roleId = req.params.roleId;
      const { permission_ids } = req.body;

      // Convert to array if needed
      let permissionIds = [];
      if (permission_ids) {
        permissionIds = Array.isArray(permission_ids) ? permission_ids : [permission_ids];
        permissionIds = permissionIds.map(id => parseInt(id));
      }

      // Update role permissions
      await permissionService.updateRolePermissions(roleId, permissionIds, req.user.id);

      res.json({
        success: true,
        message: 'Quyền của role đã được cập nhật thành công'
      });
    } catch (error) {
      console.error('Error updating role permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi cập nhật quyền của role: ' + error.message
      });
    }
  }

  // =====================================================
  // UTILITY METHODS & API ENDPOINTS
  // =====================================================

  // Sync permissions với admin tables
  async syncPermissions(req, res) {
    try {
      const result = await permissionService.syncAllTablePermissions();

      res.json({
        success: true,
        message: `Đồng bộ thành công: ${result.permissionsCreated} permissions cho ${result.tablesProcessed} bảng`,
        data: result
      });
    } catch (error) {
      console.error('Error syncing permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi đồng bộ permissions: ' + error.message
      });
    }
  }

  // API lấy permissions của user hiện tại
  async myPermissions(req, res) {
    try {
      const permissions = await permissionService.getUserPermissions(req.user.id);
      const isAdmin = await permissionService.isAdmin(req.user.id);

      res.json({
        success: true,
        data: {
          isAdmin: isAdmin,
          permissions: permissions
        }
      });
    } catch (error) {
      console.error('Error getting user permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi lấy permissions của user: ' + error.message
      });
    }
  }

  // Lấy permissions của bảng cụ thể (API)
  async getTablePermissions(req, res) {
    try {
      const { tableName } = req.params;

      const permissions = await permissionService.getTablePermissions(tableName);

      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('Error getting table permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi lấy permissions: ' + error.message
      });
    }
  }

  // Tạo permissions cho bảng mới (API)
  async createTablePermissions(req, res) {
    try {
      const { tableName, displayName } = req.body;

      if (!tableName || !displayName) {
        return res.status(400).json({
          success: false,
          message: 'Tên bảng và tên hiển thị là bắt buộc'
        });
      }

      const permissions = await permissionService.createTablePermissions(tableName, displayName);

      res.json({
        success: true,
        message: `Đã tạo ${permissions.length} permissions cho bảng ${tableName}`,
        data: permissions
      });
    } catch (error) {
      console.error('Error creating table permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi tạo permissions cho bảng: ' + error.message
      });
    }
  }

  // API: DataTables endpoint for permissions
  async api(req, res) {
    try {
      // DataTables parameters
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';
      
      // Custom filters
      const search = req.query.search_custom || searchValue;
      const table_name = req.query.table_name || '';
      const action = req.query.action || '';

      // Order
      const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
      const orderDirection = req.query.order?.[0]?.dir || 'desc';
      
      // Column mapping for ordering
      const columns = ['id', 'name', 'display_name', 'table_name', 'action', 'description'];
      const orderBy = columns[orderColumnIndex] || 'id';

      // Build WHERE conditions
      let whereConditions = [];
      let queryParams = [];

      if (search) {
        whereConditions.push('(name LIKE ? OR display_name LIKE ? OR description LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }

      if (table_name) {
        whereConditions.push('table_name = ?');
        queryParams.push(table_name);
      }

      if (action) {
        whereConditions.push('action = ?');
        queryParams.push(action);
      }

      const whereClause = whereConditions.length > 0 ? ` WHERE ${whereConditions.join(' AND ')}` : '';
      const offset = start;

      // Get data
      const [permissions, countResult] = await Promise.all([
        db.query(`
          SELECT id, name, display_name, description, table_name, action, created_at, updated_at
          FROM permissions
          ${whereClause}
          ORDER BY ${orderBy} ${orderDirection}
          LIMIT ${length} OFFSET ${offset}
        `, queryParams),
        db.queryOne(`SELECT COUNT(*) as total FROM permissions ${whereClause}`, queryParams)
      ]);

      // Format for DataTables
      const response = {
        draw: draw,
        recordsTotal: countResult.total,
        recordsFiltered: countResult.total,
        data: permissions
      };

      res.json(response);
    } catch (error) {
      console.error('Error in permissions API:', error);
      res.status(500).json({ 
        error: true, 
        message: error.message,
        draw: parseInt(req.query.draw) || 1,
        recordsTotal: 0,
        recordsFiltered: 0,
        data: []
      });
    }
  }

  // API: Get filter options
  async filterOptions(req, res) {
    try {
      const [tables, actions] = await Promise.all([
        db.query('SELECT DISTINCT table_name FROM permissions WHERE table_name IS NOT NULL ORDER BY table_name'),
        db.query('SELECT DISTINCT action FROM permissions WHERE action IS NOT NULL ORDER BY action')
      ]);

      res.json({
        success: true,
        tables,
        actions
      });
    } catch (error) {
      console.error('Error getting filter options:', error);
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }
}

module.exports = new PermissionController();
