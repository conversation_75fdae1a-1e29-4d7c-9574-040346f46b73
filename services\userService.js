const bcrypt = require('bcrypt');
const db = require('../config/database');
const commonService = require('./commonService');
const cacheService = require('./cacheService');

module.exports = {
  createUser: async (fullname, email, password) => {
    const hashedPassword = await bcrypt.hash(password, 10);
    const result = await db.query(
      'INSERT INTO user (fullname, email, password) VALUES (?, ?, ?)',
      [fullname, email, hashedPassword]
    );
    
    return { id: result.insertId, fullname, email };
  },
  
  findUserByEmail: async (email) => {
    const user = await db.queryOne(
      'SELECT * FROM user WHERE email = ?',
      [email]
    );
    return user;
  },
  
  findUserById: async (id) => {
    const user = await db.queryOne(
      'SELECT * FROM user WHERE id = ?',
      [id]
    );
    return user;
  },
  
  isValidPassword: async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
  },
  
  getAllUsers: async () => {
    return await db.query('SELECT * FROM user ORDER BY id DESC');
  },
  
  getUsersWithPagination: async (page = 1, perPage = 25, search = '') => {
    const offset = (page - 1) * perPage;
    
    let whereClause = '';
    let params = [];
    
    if (search) {
      whereClause = 'WHERE fullname LIKE ? OR email LIKE ?';
      params = [`%${search}%`, `%${search}%`];
    }
    
    // Get total count
    const countResult = await db.queryOne(
      `SELECT COUNT(*) as total FROM user ${whereClause}`,
      params
    );
    const total = countResult.total;
    
    // Get paginated data
    const data = await db.query(
      `SELECT id, fullname, email, createdAt, updatedAt 
       FROM user ${whereClause} 
       ORDER BY id DESC 
       LIMIT ? OFFSET ?`,
      [...params, perPage, offset]
    );
    
    return {
      data,
      total,
      totalFiltered: total
    };
  },
  
  editUser: async (id, userData) => {
    const { fullname, email, phone, gender, active } = userData;
    const result = await db.query(
      'UPDATE user SET fullname = ?, email = ?, phone = ?, gender = ?, active = ?, updatedAt = NOW() WHERE id = ?',
      [fullname, email, phone, gender, active, id]
    );
    return result.affectedRows > 0;
  },
  
  deleteUser: async (id) => {
    const result = await db.query('DELETE FROM user WHERE id = ?', [id]);
    return result.affectedRows > 0;
  },
  
  getUserRoles: async (userId) => {
    return await db.query(
      `SELECT r.* FROM role r 
       INNER JOIN role_user ru ON r.id = ru.role_id 
       WHERE ru.user_id = ?`,
      [userId]
    );
  },
  
  assignRoleToUser: async (userId, roleId) => {
    try {
      await db.query(
        'INSERT INTO role_user (user_id, role_id) VALUES (?, ?)',
        [userId, roleId]
      );
      return true;
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        return false; // Role already assigned
      }
      throw error;
    }
  },
  
  removeRoleFromUser: async (userId, roleId) => {
    const result = await db.query(
      'DELETE FROM role_user WHERE user_id = ? AND role_id = ?',
      [userId, roleId]
    );
    return result.affectedRows > 0;
  },
  async getUserDetails(id) {
        return await cacheService.getUserById(id, async () => {
            const [userResponse, roleResponse] = await Promise.all([
                commonService.getAllDataTable('user', {id: id}),
                commonService.getAllDataTable('role_user', {user_id: id})
            ]);

            if (!userResponse.success || !userResponse.data || userResponse.data.length === 0) {
                return null;
            }

            const user = userResponse.data[0];
            const detailUser = {
                id: user.id,
                email: user.email,
                fullname: user.fullname,
                active: user.active,
                jwt_token_id: user.jwt_token_id,
                device_info: user.device_info ? JSON.parse(user.device_info) : null,
                token_created_at: user.token_created_at,
                role_id: [],
                isAdmin: false
            };

            if (roleResponse.success && roleResponse.data) {
                for (let item of roleResponse.data) {
                    detailUser.role_id.push(item.role_id);
                    if (item.role_id == 1) {
                        detailUser.isAdmin = true;
                    }
                }
            }
            console.log('detailUser', detailUser);
            return detailUser;
        });
    }
};
