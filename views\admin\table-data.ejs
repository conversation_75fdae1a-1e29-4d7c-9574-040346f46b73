<div class="container-lg px-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="<%= table.icon || 'fas fa-table' %> me-2"></i>
                <%= table.display_name %> Data Management
            </h5>
            <div>
                <button type="button" class="btn btn-primary" data-coreui-toggle="modal" data-coreui-target="#addRecordModal" onclick="openAddModal()">
                    <i class="fas fa-plus me-1"></i>Add <%= table.display_name %>
                </button>
            </div>
        </div>
        <div class="card-body">
            <table id="data-table" class="table table-striped table-bordered" style="width:100%">
                <thead>
                    <tr>
                        <% table.columns.filter(col => col.is_visible_list).forEach(column => { %>
                            <th><%= column.display_name %></th>
                        <% }); %>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Record Modal -->
<div class="modal fade" id="addRecordModal" tabindex="-1" aria-labelledby="addRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRecordModalLabel">Add <%= table.display_name %></h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRecordForm">
                    <% table.columns.filter(col => col.is_visible_form && col.name !== 'id').forEach(column => { %>
                        <%
                        // Check if this column has a foreign key relation
                        const addRelation = table.relations.find(rel => rel.column.name === column.name);
                        %>
                        <div class="mb-3">
                            <label for="add_<%= column.name %>" class="form-label">
                                <%= column.display_name %>
                                <% if (column.validation_rules && JSON.parse(column.validation_rules).required) { %>
                                    <span class="text-danger">*</span>
                                <% } %>
                            </label>

                            <% if (addRelation) { %>
                                <!-- Foreign Key Dropdown -->
                                <select class="form-select foreign-key-select"
                                        id="add_<%= column.name %>"
                                        name="<%= column.name %>"
                                        data-foreign-table="<%= addRelation.foreign_table.name %>"
                                        data-foreign-column="<%= addRelation.foreign_column %>"
                                        data-display-column="<%= addRelation.display_column %>"
                                        <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                                    <option value="">Select <%= column.display_name %></option>
                                    <!-- Options will be loaded via AJAX -->
                                </select>
                            <% } else if (column.form_type === 'select') { %>
                                <% const rules = column.validation_rules ? JSON.parse(column.validation_rules) : {}; %>
                                <% if (rules.options) { %>
                                    <select class="form-select" id="add_<%= column.name %>" name="<%= column.name %>"
                                            <%= rules.required ? 'required' : '' %>>
                                        <option value="">Select <%= column.display_name %></option>
                                        <% rules.options.forEach(option => { %>
                                            <option value="<%= option.value %>"><%= option.label %></option>
                                        <% }); %>
                                    </select>
                                <% } %>
                            <% } else if (column.form_type === 'textarea') { %>
                                <textarea class="form-control" id="add_<%= column.name %>" name="<%= column.name %>"
                                         rows="3" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>></textarea>
                            <% } else if (column.form_type === 'checkbox') { %>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="add_<%= column.name %>" name="<%= column.name %>" value="1">
                                    <label class="form-check-label" for="add_<%= column.name %>">
                                        <%= column.display_name %>
                                    </label>
                                </div>
                            <% } else { %>
                                <input type="<%= column.form_type %>" class="form-control" id="add_<%= column.name %>"
                                       name="<%= column.name %>" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                            <% } %>
                            
                            <% if (column.validation_rules) { %>
                                <% const rules = JSON.parse(column.validation_rules); %>
                                <% if (rules.minLength || rules.maxLength) { %>
                                    <div class="form-text">
                                        <% if (rules.minLength) { %>Minimum <%= rules.minLength %> characters. <% } %>
                                        <% if (rules.maxLength) { %>Maximum <%= rules.maxLength %> characters.<% } %>
                                    </div>
                                <% } %>
                            <% } %>
                        </div>
                    <% }); %>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addRecord()">Add Record</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Record Modal -->
<div class="modal fade" id="editRecordModal" tabindex="-1" aria-labelledby="editRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRecordModalLabel">Edit <%= table.display_name %></h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editRecordForm">
                    <input type="hidden" id="editRecordId" name="id">
                    <% table.columns.filter(col => col.is_visible_form && col.name !== 'id').forEach(column => { %>
                        <%
                        // Check if this column has a foreign key relation
                        const editRelation = table.relations.find(rel => rel.column.name === column.name);
                        %>
                        <div class="mb-3">
                            <label for="edit_<%= column.name %>" class="form-label">
                                <%= column.display_name %>
                                <% if (column.validation_rules && JSON.parse(column.validation_rules).required) { %>
                                    <span class="text-danger">*</span>
                                <% } %>
                            </label>

                            <% if (editRelation) { %>
                                <!-- Foreign Key Dropdown -->
                                <select class="form-select foreign-key-select"
                                        id="edit_<%= column.name %>"
                                        name="<%= column.name %>"
                                        data-foreign-table="<%= editRelation.foreign_table.name %>"
                                        data-foreign-column="<%= editRelation.foreign_column %>"
                                        data-display-column="<%= editRelation.display_column %>"
                                        <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                                    <option value="">Select <%= column.display_name %></option>
                                    <!-- Options will be loaded via AJAX -->
                                </select>
                            <% } else if (column.form_type === 'select') { %>
                                <% const rules = column.validation_rules ? JSON.parse(column.validation_rules) : {}; %>
                                <% if (rules.options) { %>
                                    <select class="form-select" id="edit_<%= column.name %>" name="<%= column.name %>"
                                            <%= rules.required ? 'required' : '' %>>
                                        <option value="">Select <%= column.display_name %></option>
                                        <% rules.options.forEach(option => { %>
                                            <option value="<%= option.value %>"><%= option.label %></option>
                                        <% }); %>
                                    </select>
                                <% } %>
                            <% } else if (column.form_type === 'textarea') { %>
                                <textarea class="form-control" id="edit_<%= column.name %>" name="<%= column.name %>"
                                         rows="3" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>></textarea>
                            <% } else if (column.form_type === 'checkbox') { %>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_<%= column.name %>" name="<%= column.name %>" value="1">
                                    <label class="form-check-label" for="edit_<%= column.name %>">
                                        <%= column.display_name %>
                                    </label>
                                </div>
                            <% } else { %>
                                <input type="<%= column.form_type %>" class="form-control" id="edit_<%= column.name %>"
                                       name="<%= column.name %>" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                            <% } %>
                        </div>
                    <% }); %>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateRecord()">Update Record</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<script src="/js/table-data-validation.js"></script>

<script>
const tableId = <%= table.id %>;
let dataTable;

// Global variables for table configuration
const tableColumns = <%- JSON.stringify(table.columns.filter(col => col.is_visible_list)) %>;
const formColumns = <%- JSON.stringify(table.columns.filter(col => col.is_visible_form)) %>;

// Helper function to format datetime values for HTML inputs
function formatDateTimeForInput(value, type) {
    if (!value) return '';

    const date = new Date(value);
    if (isNaN(date.getTime())) return value;

    if (type === 'date') {
        // Format as YYYY-MM-DD
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    } else if (type === 'datetime' || type === 'timestamp') {
        // Format as YYYY-MM-DDTHH:MM
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0') + 'T' +
               String(date.getHours()).padStart(2, '0') + ':' +
               String(date.getMinutes()).padStart(2, '0');
    }

    return value;
}

// Helper function to process form data
function processFormData(formId) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    const data = {};

    // Process form data
    for (let [key, value] of formData.entries()) {
        if (key !== 'id') {
            data[key] = value;
        }
    }

    // Handle checkboxes
    const prefix = formId.replace('RecordForm', '');
    <% table.columns.filter(col => col.is_visible_form && col.form_type === 'checkbox').forEach(column => { %>
        data['<%= column.name %>'] = document.getElementById(`${prefix}_<%= column.name %>`).checked ? 1 : 0;
    <% }); %>

    return data;
}

$(document).ready(function() {
    // Initialize DataTable
    const dataTableColumns = [
        <% table.columns.filter(col => col.is_visible_list).forEach((column, index) => { %>
            {
                data: '<%= column.name %>',
                name: '<%= column.name %>',
                title: '<%= column.display_name %>'
            },
        <% }); %>
        {
            data: 'actions',
            name: 'actions',
            title: 'Actions',
            orderable: false,
            searchable: false
        }
    ];

    dataTable = $('#data-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: `/admin/tables/${tableId}/data/api`,
            type: 'GET'
        },
        columns: dataTableColumns,
        order: [[0, 'desc']],
        pageLength: 25
    });

    // Load foreign key dropdown data when modals are shown
    $('#addRecordModal').on('show.coreui.modal show.bs.modal', function() {
        console.log('Add modal opening, loading foreign key dropdowns...');
        loadForeignKeyDropdowns('add');
    });

    $('#editRecordModal').on('show.coreui.modal show.bs.modal', function() {
        console.log('Edit modal opening, loading foreign key dropdowns...');
        loadForeignKeyDropdowns('edit');
    });

    // Pre-load foreign key dropdowns for better UX
    setTimeout(() => {
        loadForeignKeyDropdowns('add');
    }, 500);

    // Setup real-time validation
    TableDataValidator.setupRealTimeValidation(formColumns);
});

// Function to load foreign key dropdown data
async function loadForeignKeyDropdowns(prefix) {
    const foreignKeySelects = document.querySelectorAll(`#${prefix}RecordForm .foreign-key-select`);

    if (foreignKeySelects.length === 0) {
        console.log(`No foreign key selects found for prefix: ${prefix}`);
        return true;
    }

    const promises = [];

    for (const select of foreignKeySelects) {
        // Skip if already loaded
        if (select.getAttribute('data-loaded') === 'true') {
            continue;
        }

        const columnName = select.name;
        const foreignTable = select.dataset.foreignTable;
        const displayColumn = select.dataset.displayColumn;

        if (!foreignTable || !displayColumn) {
            console.warn(`Missing foreign table or display column for ${columnName}`);
            continue;
        }

        const url = `/admin/tables/${tableId}/relations/${columnName}/dropdown`;

        const promise = fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(result => {
                if (result.success && result.data) {
                    // Clear existing options except the first one
                    const firstOption = select.querySelector('option:first-child');
                    select.innerHTML = '';
                    if (firstOption) {
                        select.appendChild(firstOption);
                    } else {
                        // Add default empty option
                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.textContent = `Select ${select.dataset.displayColumn || columnName}`;
                        select.appendChild(defaultOption);
                    }

                    // Add new options
                    result.data.forEach(item => {
                        const option = document.createElement('option');
                        option.value = String(item.value);
                        option.textContent = item.label;
                        select.appendChild(option);
                    });

                    // Mark as loaded
                    select.setAttribute('data-loaded', 'true');
                    return { success: true, count: result.data.length };
                } else {
                    throw new Error(result.message || 'No data returned');
                }
            })
            .catch(error => {
                console.error(`Error loading dropdown data for ${columnName}:`, error);
                select.setAttribute('data-loaded', 'false');

                // Add error option
                select.innerHTML = '<option value="">Error loading options</option>';
                return { success: false, error: error.message };
            });

        promises.push(promise);
    }

    // Wait for all dropdowns to be loaded
    const results = await Promise.all(promises);
    console.log('All foreign key dropdowns processed:', results);

    // Return success status
    return results.every(result => result && result.success !== false);
}

// Helper function to set foreign key value with retry mechanism
function setForeignKeyValue(elementId, value, maxRetries = 5) {
    return new Promise((resolve) => {
        let attempts = 0;

        function trySetValue() {
            attempts++;
            const element = document.getElementById(elementId);

            if (!element) {
                console.error(`Element not found: ${elementId}`);
                resolve(false);
                return;
            }

            // Check if dropdown is loaded
            const isLoaded = element.getAttribute('data-loaded') === 'true';
            const hasOptions = element.options.length > 1;

            if (!isLoaded || !hasOptions) {
                // Dropdown not loaded yet, retry
                if (attempts < maxRetries) {
                    setTimeout(trySetValue, 200); // Increased delay
                    return;
                } else {
                    console.warn(`Dropdown ${elementId} failed to load after ${maxRetries} attempts`);
                    resolve(false);
                    return;
                }
            }

            // Set the value
            const stringValue = value ? String(value) : '';
            element.value = stringValue;

            // Verify the value was set correctly
            if (stringValue && element.value !== stringValue) {
                // Try to find and select the option manually
                const options = element.options;
                for (let i = 0; i < options.length; i++) {
                    if (options[i].value === stringValue) {
                        element.selectedIndex = i;
                        break;
                    }
                }
            }

            resolve(true);
        }

        trySetValue();
    });
}

// Function to open add modal and load dropdowns
window.openAddModal = async function() {
    // Clear form
    document.getElementById('addRecordForm').reset();

    // Clear any previous validation errors
    TableDataValidator.clearErrors();

    // Load foreign key dropdowns
    await loadForeignKeyDropdowns('add');
}
// Add new record
window.addRecord = async function() {
    try {
        // Process form data
        const data = processFormData('addRecordForm');

        // Frontend validation
        const validation = TableDataValidator.validateForm(data, formColumns);

        if (validation.hasErrors) {
            TableDataValidator.displayErrors(validation.errors);
            return;
        }

        // Clear any previous errors
        TableDataValidator.clearErrors();
        
        const response = await fetch(`/admin/tables/${tableId}/records`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Record added successfully!');
            $('#addRecordModal').modal('hide');
            dataTable.ajax.reload();
            document.getElementById('addRecordForm').reset();
        } else {
            if (result.errors && Array.isArray(result.errors)) {
                // Display validation errors
                const errorObj = {};
                result.errors.forEach(error => {
                    if (error.field) {
                        errorObj[error.field] = [error.message];
                    }
                });
                TableDataValidator.displayErrors(errorObj);
            } else {
                alert('Error adding record: ' + result.message);
            }
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error adding record');
    }
}

// Edit record
window.editRecord = async function(id) {
    try {
        const response = await fetch(`/admin/tables/${tableId}/records/${id}`);
        const result = await response.json();

        if (result.success) {
            const record = result.data;
            console.log('Edit record data:', record);
            document.getElementById('editRecordId').value = record.id;

            // Load foreign key dropdowns first
            const dropdownsLoaded = await loadForeignKeyDropdowns('edit');
            console.log('Dropdowns loaded successfully:', dropdownsLoaded);

            // Add small delay to ensure dropdowns are fully populated
            await new Promise(resolve => setTimeout(resolve, 200));

            // Fill form fields with improved foreign key handling
            const fieldPromises = [];

            <% table.columns.filter(col => col.is_visible_form && col.name !== 'id').forEach(column => { %>
                <% const jsRelation = table.relations.find(rel => rel.column.name === column.name); %>
                <% if (jsRelation) { %>
                    // Foreign key field - use helper function for reliable setting
                    const recordValue_<%= column.name %> = record['<%= column.name %>'];
                    console.log('Setting foreign key <%= column.name %> to value:', recordValue_<%= column.name %>);
                    fieldPromises.push(setForeignKeyValue('edit_<%= column.name %>', recordValue_<%= column.name %>));
                <% } else if (column.form_type === 'checkbox') { %>
                    const checkboxElement_<%= column.name %> = document.getElementById('edit_<%= column.name %>');
                    if (checkboxElement_<%= column.name %>) {
                        checkboxElement_<%= column.name %>.checked = record['<%= column.name %>'] == 1;
                    }
                <% } else { %>
                    const fieldElement_<%= column.name %> = document.getElementById('edit_<%= column.name %>');
                    if (fieldElement_<%= column.name %>) {
                        let value = record['<%= column.name %>'] || '';

                        // Format datetime values for HTML input
                        <% if (['date', 'datetime', 'timestamp'].includes(column.type)) { %>
                            value = formatDateTimeForInput(value, '<%= column.type %>');
                        <% } %>

                        fieldElement_<%= column.name %>.value = value;
                    }
                <% } %>
            <% }); %>

            // Wait for all foreign key fields to be set
            if (fieldPromises.length > 0) {
                const results = await Promise.all(fieldPromises);
                console.log('Foreign key field setting results:', results);
            }

            $('#editRecordModal').modal('show');
        } else {
            alert('Error loading record: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error loading record');
    }
}

// Update record
window.updateRecord = async function() {
    try {
        const recordId = document.getElementById('editRecordId').value;

        // Process form data
        const data = processFormData('editRecordForm');

        // Frontend validation
        const validation = TableDataValidator.validateForm(data, formColumns);

        if (validation.hasErrors) {
            TableDataValidator.displayErrors(validation.errors);
            return;
        }

        // Clear any previous errors
        TableDataValidator.clearErrors();
        
        const response = await fetch(`/admin/tables/${tableId}/records/${recordId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Record updated successfully!');
            $('#editRecordModal').modal('hide');
            dataTable.ajax.reload();
        } else {
            if (result.errors && Array.isArray(result.errors)) {
                // Display validation errors
                const errorObj = {};
                result.errors.forEach(error => {
                    if (error.field) {
                        errorObj[error.field] = [error.message];
                    }
                });
                TableDataValidator.displayErrors(errorObj);
            } else {
                alert('Error updating record: ' + result.message);
            }
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error updating record');
    }
}

// Delete record
window.deleteRecord = async function(id) {
    try {
        // Kiểm tra related records trước
        const relatedResponse = await fetch(`/admin/tables/${tableId}/records/${id}/related`);
        const relatedResult = await relatedResponse.json();

        if (relatedResult.success && relatedResult.hasRelatedRecords) {
            // Hiển thị thông tin chi tiết về related records
            let message = '⚠️ CASCADE DELETE WARNING ⚠️\n\n';
            message += 'This record has related data in other tables:\n\n';

            let totalRelatedRecords = 0;
            relatedResult.relatedRecords.forEach(related => {
                totalRelatedRecords += related.count;
                message += `📋 ${related.table}: ${related.count} record(s)\n`;

                // Hiển thị sample data nếu có
                if (related.samples && related.samples.length > 0) {
                    const sample = related.samples[0];
                    const sampleKeys = Object.keys(sample).slice(0, 2); // Chỉ hiển thị 2 field đầu
                    const sampleData = sampleKeys.map(key => `${key}: ${sample[key]}`).join(', ');
                    message += `   Example: ${sampleData}\n`;
                }
            });

            message += `\n🔥 TOTAL IMPACT: ${totalRelatedRecords + 1} records will be deleted\n`;
            message += '\n❗ This action will permanently delete:\n';
            message += '• The selected record\n';
            message += '• ALL related records in other tables\n';
            message += '\n⚠️ THIS CANNOT BE UNDONE!\n\n';
            message += 'Type "DELETE" to confirm:';

            const confirmation = prompt(message);
            if (confirmation !== 'DELETE') {
                alert('Delete operation cancelled.');
                return;
            }
        } else {
            // Xác nhận xóa thông thường
            if (!confirm('Are you sure you want to delete this record? This action cannot be undone.')) {
                return;
            }
        }

        // Thực hiện xóa
        const deleteResponse = await fetch(`/admin/tables/${tableId}/records/${id}`, {
            method: 'DELETE',
            headers: {}
        });

        const deleteResult = await deleteResponse.json();

        if (deleteResult.success) {
            alert('Record deleted successfully!');
            dataTable.ajax.reload();
        } else {
            alert('Error deleting record: ' + deleteResult.message);
        }

    } catch (error) {
        console.error('Error:', error);
        alert('Error deleting record: ' + error.message);
    }
}

// Event delegation for dynamically created buttons
$(document).on('click', '.edit-record', function() {
    const id = $(this).data('id');
    editRecord(id);
});

$(document).on('click', '.delete-record', function() {
    const id = $(this).data('id');
    deleteRecord(id);
});
</script>
